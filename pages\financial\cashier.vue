<template>
  <view class="cashier-page">
    <!-- 顶部信息区域 -->
    <view class="top-card">
      <view class="top-title">扣款金额</view>
      <view class="top-amount">
        ¥<text class="top-amount-num">{{ amount }}</text>
      </view>
      <view class="top-pay-way">
        <view class="pay-way-title">还款账户</view>
        <view class="pay-way-item">中国建设银行(5755)</view>
      </view>
      <view class="pay-tips">
        预计2个工作日内完成
      </view>
      <!-- <view class="top-timer">
        支付剩余时间：
        <u-count-down
          :show-days="false"
          :show-border="false"
          font-size="28"
          color="#000"
          border-color="#fff"
          :timestamp="autoCancel"
        ></u-count-down>
      </view> -->
    </view>

    <!-- 底部确认支付按钮 -->
    <view class="pay-btn-box">
      <button class="pay-btn-gradient" @click="onConfirmPay">立即支付</button>
      <view class="security-tip">
        <image src="/static/financial/desc.png" mode="scaleToFill" />
        平台承诺保障您的信息安全
      </view>
    </view>
    
    <!-- 验证码弹窗 -->
    <u-popup v-model="showSms" mode="center" border-radius="16" width="600rpx">
      <view class="sms-modal">
        <view class="sms-header">
          <text class="sms-title">还款验证</text>
          <u-icon name="close" size="32" @click="showSms = false" style="position:absolute;right:0;top:0;"></u-icon>
        </view>
        <view class="sms-desc">验证码将发送至手机{{ phone }}，请注意查收</view>
        <view class="sms-row">
          <input class="sms-input" v-model="smsCode" maxlength="6" placeholder="请输入验证码" placeholder-class="sms-placeholder" />
          <text class="sms-get" :class="{disabled: smsCountdown>0}" @click="getSmsCode">{{ smsCountdown>0 ? smsCountdown+'s后重发' : '获取验证码' }}</text>
        </view>
        <button class="sms-btn" @click="submitSms">确认</button>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { sendMobile_Can } from '@/api/login';
import { mapState } from 'vuex';
export default {
  name: "Cashier",
  data() {
    return {
      amount: 6525, // mock 金额
      autoCancel: 15 * 60, // 15分钟倒计时，单位秒
      payList: [
        { value: "BANK", name: "银行卡" },
        { value: "ALIPAY", name: "支付宝" },
        { value: "WECHAT", name: "微信支付" },
      ],
      selectedPay: "BANK",
      // 验证码相关
      showSms: false,
      smsCode: '',
      smsCountdown: 0,
      smsTimer: null,
    };
  },
  beforeDestroy() {
    clearInterval(this.smsTimer);
  },
  computed: {
    ...mapState(['userInfo']),
    phone() {
      const mobile = this.userInfo.mobile || this.userInfo.phone || '';
      if (!mobile) return '';
      // 脱敏处理
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },
    rawPhone() {
      return this.userInfo.mobile || this.userInfo.phone || '';
    },
  },
  methods: {
    selectPay(val) {
      this.selectedPay = val;
    },
    onConfirmPay() {
      if (!this.selectedPay) {
        uni.showToast({ title: "请选择支付方式", icon: "none" });
        return;
      }
      this.showSms = true;
    },
    async getSmsCode() {
      if (this.smsCountdown > 0) return;
      if (!this.rawPhone || !/^1[3-9]\d{9}$/.test(this.rawPhone)) {
        uni.showToast({ title: '手机号无效', icon: 'none' });
        return;
      }
      try {
        await sendMobile_Can('STAGE_REPAY');
        uni.showToast({ title: '验证码已发送', icon: 'none' });
        this.smsCountdown = 60;
        this.smsTimer = setInterval(() => {
          if (this.smsCountdown > 0) {
            this.smsCountdown--;
          } else {
            clearInterval(this.smsTimer);
          }
        }, 1000);
      } catch (e) {
        uni.showToast({ title: '验证码发送失败', icon: 'none' });
      }
    },
    submitSms() {
      if (!this.smsCode) {
        uni.showToast({ title: '请输入验证码', icon: 'none' });
        return;
      }
      if (!/^\d{6}$/.test(this.smsCode)) {
        uni.showToast({ title: '验证码格式错误', icon: 'none' });
        return;
      }
      
      uni.showLoading({ title: '正在处理...' });
      
      setTimeout(() => {
        uni.hideLoading();
        this.showSms = false;
        this.smsCode = '';

        uni.navigateTo({
          url: `/pages/financial/repay-success?amount=${this.amount}`
        });
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.cashier-page {
  padding: 20rpx 32rpx 0;
}
.top-card {
  width: 686rpx;
  height: 322rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  .top-title {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    margin-bottom: 20rpx;
    text-align: center;
  }
  .top-amount {
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 12rpx;
    text-align: center;
    text {
        font-size: 56rpx;
    }
  }
  .top-pay-way {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 40rpx;
    .pay-way-title {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
    }
    .pay-way-item {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }
  }
  .pay-tips {
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    margin-top: 12rpx;
  }
  .top-timer {
    font-size: 24rpx;
    color: #999;
  }
}

.pay-btn-box {
    width: 750rpx;
    height: 206rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0,0,0,0.04);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 32rpx 0;
  .pay-btn-gradient {
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 78rpx;
    font-size: 32rpx;
    color: #fff;
    text-align: center;
    line-height: 100rpx;
    border: none;
    margin-bottom: 16rpx;
  }
  .security-tip {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #ff9500;
    justify-content: center;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
}
.sms-modal {
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  min-width: 540rpx;
}
.sms-header {
  position: relative;
  text-align: center;
  margin-bottom: 20rpx;
}
.sms-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.sms-desc {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 26rpx;
  text-align: center;
}
.sms-row {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 40rpx;
}
.sms-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 28rpx;
  color: #222;
  padding: 20rpx 0;
}
.sms-placeholder {
  color: #cccccc;
  font-size: 28rpx;
}
.sms-get {
  color: #ff5134;
  font-size: 28rpx;
  margin-left: 20rpx;
}
.sms-get.disabled {
  color: #ccc;
}
.sms-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50rpx;
  border: none;
  margin-top: 20rpx;
  font-weight: 400;
}
</style>
