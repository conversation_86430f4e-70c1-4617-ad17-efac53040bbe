<template>
  <view class="bill-detail-page">
    <!-- 借款信息 -->
    <view class="bill-detail-amount-box">
      <view class="bill-detail-amount-label">借款本金(元)</view>
      <view class="bill-detail-amount-value">{{ loanDetail.amount || '0.00' }}</view>
      <view class="bill-detail-amount-term">借款期数(月) </view>
      <view class="bill-detail-amount-term-value">{{ loanDetail.periods || 0 }}</view>
    </view>
    <!-- 关联订单 -->
    <view class="bill-detail-order-box" @click="goToOrderDetail">
      <view class="bill-title-box">
        <view class="bill-detail-order-label">关联订单</view>
        <view class="bill-detail-order-row">
          <view>商品名称 <text>{{ loanDetail.remark || '未知商品' }}</text></view>
          <view>订单号 <text>{{ loanDetail.orderNo || '未知订单号' }}</text></view>
        </view>
      </view>
      <u-icon name="arrow-right" color="#000000" size="28"></u-icon>
    </view>
    <!-- 还款计划 -->
    <view class="bill-detail-plan-box">
      <view
        v-for="(plan, idx) in repayPlans"
        :key="idx"
        :class="[
          'bill-detail-plan-item',
          { overdue: plan.status === 'overdue' },
        ]"
      >
        <view class="plan-left">
          <view class="plan-title"
            >第{{ plan.period }}期 | 还款日 {{ plan.repayDate }}</view
          >
          <view class="plan-amount">{{ plan.amount }}</view>
          <view v-if="plan.status === 'overdue'" class="plan-status overdue">
            已逾期{{ plan.overdueDays }}天，请尽快还款
          </view>
        </view>
        <view
          class="plan-btn"
          :class="{
            waiting: plan.status === 'waiting',
            not_ready: plan.status === 'not_ready'
          }"
          @click="(plan.status === 'overdue') ? showRepayModal(plan) : null"
        >
          {{
            plan.status === "overdue"
              ? "立即还款"
              : plan.status === "waiting"
              ? "待还款"
              : "已还款"
          }}
        </view>
      </view>
    </view>
    <!-- <view style="height: 200rpx"></view> -->
    <!-- 提前结清按钮 -->
    <!-- <view class="bill-detail-footer">
      <view class="settle-btn" @click="showSettleModal">提前结清</view>
    </view> -->

    <u-popup
      v-model="showCreditModal"
      mode="bottom"
      border-radius="16"
      :closeable="true"
    >
      <view class="credit-modal">
        <view class="modal-title">{{ currentRepayPlan ? '立即还款' : '提前结清' }}</view>
        <view class="modal-amount">
          <text>¥ </text> {{ currentRepayPlan ? currentRepayPlan.amount : '0.00' }}
          <view class="plan-status-icon" @click="showPlanDetail(currentRepayPlan)">
            <u-icon
              name="info-circle"
              color="#DDDDDD"
              size="48"
            ></u-icon> </view
        ></view>
        <view class="modal-section-title">还款方式</view>
        <view class="modal-bank-list">
          <view
            v-for="(bank, idx) in bankList"
            :key="idx"
            class="modal-bank-item"
            @click="selectBank(idx)"
          >
            <image
              src="/static/financial/bank-icon.png"
              style="width: 40rpx; height: 40rpx; margin-right: 16rpx"
            />
            {{ bank.name }} {{ bank.type }}({{ bank.last4 }})
            <!-- 选中时显示图片，否则显示空心圆 -->
            <template v-if="selectedBank === idx">
              <image src="/static/financial/step_Act.png" class="select-icon" />
            </template>
            <template v-else>
              <view class="yuan"></view>
            </template>
          </view>
          <!-- <view class="modal-bank-item" @click="addNewCard">
            <u-icon name="plus" size="32"></u-icon>
            <text style="margin-left: 12rpx">使用新卡</text>
          </view> -->
        </view>
        <!-- <view class="modal-section-title">支付方式</view>
        <view class="modal-pay-list">
          <view
            v-for="(pay, idx) in payList"
            :key="idx"
            class="modal-pay-item"
            @click="selectPay(idx)"
          >
            <u-icon :name="pay.icon" :color="pay.color" size="40"></u-icon>
            <text style="margin-left: 16rpx">{{ pay.name }}</text>
            <template v-if="selectedPay === idx">
              <image src="/static/financial/step_Act.png" class="select-icon" />
            </template>
            <template v-else>
              <view class="yuan"></view>
            </template>
          </view>
        </view> -->
        <view
          class="modal-btn"
          @click="onSettle('O202507101943120927353950209')"
          >完成</view
        >
      </view>
    </u-popup>

    <u-popup
      v-model="showDetailModal"
      mode="center"
      border-radius="16"
      :closeable="true"
    >
      <view class="detail-modal">
        <view class="modal-title">待还明细</view>
        <view class="modal-row">
          <view>本金</view>
          <view>{{ currentDetail && currentDetail.principal }}</view>
        </view>
        <view class="modal-row">
          <view>利息</view>
          <view>{{ currentDetail && currentDetail.interest }}</view>
        </view>
        <view class="modal-row">
          <view>担保费</view>
          <view>{{ currentDetail && currentDetail.guarantee }}</view>
        </view>
        <view class="modal-row">
          <view>违约金</view>
          <view>{{ currentDetail && currentDetail.penalty }}</view>
        </view>
        <view class="modal-btn" @click="showDetailModal = false">确认</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getPeriodsDetailInfo } from '@/api/common.js';
import { formatISOTime } from '@/utils/filters.js';

export default {
  name: "BillDetail",
  data() {
    return {
      // 借款ID，从页面参数获取
      loanId: null,
      // 借款详情数据
      loanDetail: {
        amount: 0,
        orderNo: '',
        periods: 0,
        remark: ''
      },
      // 分期还款计划
      repayPlans: [],
      showCreditModal: false,
      bankList: [
        { name: "招商银行", type: "储蓄卡", last4: "5755" },
        { name: "招商银行", type: "储蓄卡", last4: "5755" },
      ],
      payList: [
        { name: "支付宝", icon: "zhifubao-circle-fill", color: "#1677ff" },
        { name: "微信支付", icon: "weixin-circle-fill", color: "#07c160" },
      ],
      selectedBank: null, // 选中的银行卡下标
      selectedPay: null, // 选中的支付方式下标
      showDetailModal: false,
      currentDetail: null,
      currentRepayPlan: null, // 当前选中的还款计划
    };
  },
  onLoad(options) {
    // 获取页面参数中的 loanId
    if (options.loanId) {
      // 保持为字符串，避免精度丢失
      this.loanId = options.loanId;
      this.loadPeriodsDetail();
    } else {
      uni.showToast({
        title: '缺少借款ID参数',
        icon: 'none',
        duration: 2000
      });
    }
  },
  methods: {
    // 加载分期还款明细
    loadPeriodsDetail() {
      if (!this.loanId) {
        console.error('loanId 不能为空');
        return;
      }

      uni.showLoading({
        title: '加载中...'
      });

      getPeriodsDetailInfo({ loanId: this.loanId }).then(res => {
        console.log('分期还款明细', res);
        if (res && res.data && res.data.success && res.data.result) {
          const result = res.data.result;

          // 设置借款详情
          this.loanDetail = {
            amount: result.amount || 0,
            orderNo: result.orderNo || '',
            periods: result.periods || 0,
            remark: result.remark || ''
          };

          // 处理分期信息
          if (result.PeriodsInfos && result.PeriodsInfos.length > 0) {
            this.repayPlans = result.PeriodsInfos.map((period, index) => {
              // 添加调试信息
              console.log(`期数${index + 1}:`, {
                status: period.status,
                canRepay: period.canRepay,
                overdueStatus: period.overdueStatus,
                gmtPlanRepay: period.gmtPlanRepay
              });

              // 根据状态判断还款状态
              let status = 'waiting';
              if (period.status === 'FINISHED') {
                status = 'finished';
              } else if (period.overdueStatus === 'Y') {
                status = 'overdue';
              } else if (period.status === 'AWAIT_REPAY' && period.canRepay === true) {
                status = 'waiting';
              } 

              return {
                period: index + 1, // 期数从1开始
                repayDate: period.gmtPlanRepay
                  ? formatISOTime(period.gmtPlanRepay, '{y}-{m}-{d}') || '未知日期'
                  : '未知日期',
                amount: period.amount || '0.00',
                overdue: period.overdueStatus === 'Y',
                overdueDays: period.overdueDays || 0,
                status: status,
                canRepay: period.canRepay, // 保存canRepay字段
                // 保存原始数据用于详情显示
                originalData: period
              };
            });
          }
        }
      }).catch(err => {
        console.error('获取分期还款明细失败', err);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none',
          duration: 2000
        });
      }).finally(() => {
        uni.hideLoading();
      });
    },
    selectBank(idx) {
      this.selectedBank = idx;
      this.selectedPay = null; // 取消支付方式
    },
    selectPay(idx) {
      console.log("@@@@@@@@@@@", idx);

      this.selectedPay = idx;
      this.selectedBank = null; // 取消银行卡
    },
    addNewCard() {
      // 跳转添加新卡逻辑
    },
    onSettle(val) {
      // 校验是否选中银行卡
      if (this.selectedBank === null) {
        uni.showToast({
          title: '请先选择银行卡',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 完成结清逻辑
      this.showCreditModal = false;
      if (val) {
        // #ifdef MP-WEIXIN
        new LiLiWXPay({
          sn: val.sn,
          price: val.flowPrice,
          orderType: "ORDER",
        }).pay();
        // #endif
        // #ifndef MP-WEIXIN
        //跳转支付页面
        uni.navigateTo({
          url: "/pages/financial/cashier?order_sn=" + val,
        });
        // #endif
      }
    },
    showPlanDetail(plan) {
      console.log("查看计划详情：", plan);

      // 根据当前选中的还款计划获取详情数据
      let currentPlan = null;
      if (typeof plan === 'number') {
        // 如果传入的是期数，查找对应的还款计划
        currentPlan = this.repayPlans.find(p => p.period === plan);
      } else if (plan && plan.originalData) {
        // 如果传入的是还款计划对象
        currentPlan = plan;
      }

      if (currentPlan && currentPlan.originalData) {
        const originalData = currentPlan.originalData;
        this.currentDetail = {
          principal: originalData.amount || 0, // 本金
          interest: originalData.interestFee || 0, // 利息
          guarantee: originalData.serviceFee || 0, // 服务费（担保费）
          penalty: originalData.overdueFee || 0, // 逾期费用（违约金）
        };
      } else {
        // 默认数据
        this.currentDetail = {
          principal: 0,
          interest: 0,
          guarantee: 0,
          penalty: 0,
        };
      }

      this.showDetailModal = true;
    },
    showRepayModal(plan) {
      this.currentRepayPlan = plan;
      uni.navigateTo({
          url: "/pages/financial/cashier?order_sn=" + 123,
      });
      // this.showCreditModal = true;
    },
    showSettleModal() {
      this.currentRepayPlan = null;
      this.showCreditModal = true;
    },
    // 跳转到订单详情页面
    goToOrderDetail() {
      if (this.loanDetail.orderNo) {
        uni.navigateTo({
          url: `/pages/order/orderDetail?sn=${this.loanDetail.orderNo}`
        });
      } else {
        uni.showToast({
          title: '订单号不存在',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.bill-detail-page {
  padding: 20rpx 32rpx;
  background: #f7f8fa;
}
.bill-detail-amount-box {
  width: 100%;
  background: url("/static/financial/billBg.png") no-repeat;
  background-size: 100% 100%;
  width: 686rpx;
  height: 294rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;

  .bill-detail-amount-label {
    font-weight: 400;
    font-size: 32rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }
  .bill-detail-amount-value {
    font-weight: 500;
    font-size: 52rpx;
    color: #000000;
    margin-bottom: 12rpx;
  }
  .bill-detail-amount-term {
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 6rpx;
  }
  .bill-detail-amount-term-value {
    font-weight: 500;
    font-size: 36rpx;
    color: #000000;
  }
}
.bill-detail-order-box {
  width: 100%;
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .bill-detail-order-label {
    font-weight: 400;
    font-size: 32rpx;
    color: #666666;
    margin-bottom: 20rpx;
  }
  .bill-detail-order-row {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    margin-top: 12rpx;
    view {
      text {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        margin-left: 16rpx;
      }
      margin-bottom: 12rpx;
    }
  }
}
.bill-detail-plan-box {
  width: 100%;
  background: #fff;
  border-radius: 20rpx;
  padding: 0 32rpx;
  margin-bottom: 20rpx;

  .bill-detail-plan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 32rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    position: relative;
    &:last-child {
      border-bottom: none;
    }
    .plan-left {
      flex: 1;
      .plan-title {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 8rpx;
        padding-top: 32rpx;
      }
      .plan-amount {
        font-weight: 500;
        font-size: 44rpx;
        color: #000000;
      }
      .plan-status {
        font-weight: 400;
        font-size: 24rpx;
        color: #ff5134;
      }
    }
    .plan-btn {
      width: 164rpx;
      height: 64rpx;
      background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
      border-radius: 234rpx;
      font-size: 28rpx;
      color: #fff;
      text-align: center;
      line-height: 62rpx;
      margin-top: 32rpx;
      &.waiting {
        background: #fff;
        color: #999;
        border: 1rpx solid rgba(0, 0, 0, 0.1);
      }
      &.not_ready {
        background: #f5f5f5;
        color: #ccc;
        border: 1rpx solid #e5e5e5;
        cursor: not-allowed;
      }
    }
    &.overdue {
      .plan-status {
        color: #ff5134;
      }
    }
  }
  
}
.bill-detail-footer {
  width: 100%;
  height: 180rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  margin-top: 40rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: center;
  align-items: center;
  .settle-btn {
    width: 670rpx;
    height: 100rpx;
    border-radius: 86rpx 86rpx 86rpx 86rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
    text-align: center;
    line-height: 100rpx;
  }
}
.credit-modal {
  padding: 32rpx 24rpx 24rpx 24rpx;
  .modal-title {
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    margin-bottom: 20rpx;
  }
  .modal-amount {
    font-weight: 600;
    font-size: 56rpx;
    color: #333333;
    text-align: center;
    margin-bottom: 24rpx;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    // position: relative;
    text {
      font-size: 32rpx;
      color: #333333;
      margin-bottom: 6rpx;
      margin-right: 8rpx;
    }
    .plan-status-icon {
      margin-left: 20rpx;
    }
  }
  .modal-section-title {
    font-weight: 400;
    font-size: 32rpx;
    color: #999999;
    margin: 24rpx 0 12rpx 0;
  }
  .modal-bank-list,
  .modal-pay-list {
    border-radius: 12rpx;
    padding: 12rpx 0;
    margin-bottom: 12rpx;
  }
  .modal-bank-item,
  .modal-pay-item {
    display: flex;
    align-items: center;
    padding: 18rpx 24rpx;
    font-size: 28rpx;
    color: #333;
    position: relative;
    &:last-child {
      border-bottom: none;
    }
    .modal-checkbox {
      margin-left: auto;
    }
  }
  .modal-btn {
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 78rpx 78rpx 78rpx 78rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #ffffff;
    text-align: center;
    line-height: 100rpx;
    margin: 24rpx auto 0;
  }
}
.select-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: auto;
}
.yuan {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  margin-left: auto;
}
.detail-modal {
  width: 576rpx;
  padding: 32rpx;
  .modal-title {
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 24rpx;
  }
  .modal-row {
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
    margin-bottom: 16rpx;
  }
  .modal-btn {
    width: 100%;
    height: 84rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 40rpx;
    color: #fff;
    text-align: center;
    line-height: 84rpx;
    margin-top: 32rpx;
  }
}
</style>
