# 详细地址验证功能说明

## 功能概述

在身份认证页面的完善信息步骤中，对详细地址字段添加长度验证，要求必须大于7个字符。

## 修改内容

### 1. nextStep() 方法验证

**位置**: `pages/financial/idAuth.vue` 第1081-1084行

**添加的验证逻辑**:
```javascript
if (!f.address)
  return uni.showToast({ title: "请输入详细地址", icon: "none" });
if (f.address.length <= 7)
  return uni.showToast({ title: "详细地址必须大于7个字符", icon: "none" });
```

**说明**: 用户点击"下一步"按钮时进行验证

### 2. canSubmitInfo 计算属性

**位置**: `pages/financial/idAuth.vue` 第895-908行

**修改的验证条件**:
```javascript
canSubmitInfo() {
  const f = this.infoForm;
  return (
    f.education &&
    f.marriage &&
    f.city &&
    f.address &&
    f.address.length > 7 &&  // 新增长度验证
    f.income &&
    f.industry &&
    f.job
  );
}
```

**说明**: 控制"下一步"按钮的可用状态

## 验证规则

### 字符长度要求
- **最小长度**: 8个字符（大于7个字符）
- **验证时机**: 
  1. 实时验证（计算属性）
  2. 提交验证（nextStep方法）

### 错误提示
- **空值提示**: "请输入详细地址"
- **长度不足提示**: "详细地址必须大于7个字符"

## 用户体验

### 按钮状态控制
- ✅ **地址长度 > 7**: 按钮可点击
- ❌ **地址长度 ≤ 7**: 按钮禁用状态

### 提交时验证
- ✅ **验证通过**: 进入下一步
- ❌ **验证失败**: 显示错误提示，停留当前页面

## 测试用例

### 有效地址示例
```
✅ "北京市朝阳区建国门外大街1号"  (长度: 16)
✅ "上海市浦东新区陆家嘴环路1000号" (长度: 18)
✅ "广州市天河区珠江新城花城大道" (长度: 15)
```

### 无效地址示例
```
❌ ""           (空值)
❌ "北京市"      (长度: 3)
❌ "朝阳区建国门"  (长度: 6)
❌ "建国门外大街1"  (长度: 7)
```

## 实现特点

1. **双重验证**: 计算属性 + 方法验证
2. **实时反馈**: 按钮状态实时更新
3. **友好提示**: 明确的错误信息
4. **用户引导**: 清晰的字符数要求

## 注意事项

1. 验证使用 `length` 属性，按字符计算（包括中文字符）
2. 空格和特殊字符也计入长度
3. 验证条件为 `> 7`，即至少需要8个字符
4. 计算属性会实时响应输入变化，自动更新按钮状态
